// src/lib/wallet-validation.ts
// Wallet connection and validation utilities

import { useAccount, useChainId, useConnect, useDisconnect } from 'wagmi';
import { useConnectModal } from '@rainbow-me/rainbowkit';
import { notifications } from '@mantine/notifications';
import { ODUDE_SDK_CONFIG, consoleLog, consoleError } from './config';
import { validateAndSwitchNetwork, isBaseSepolia } from './network-validation';

/**
 * Wallet connection status
 */
export interface WalletStatus {
  isConnected: boolean;
  address?: string;
  chainId?: number;
  isCorrectNetwork: boolean;
  needsNetworkSwitch: boolean;
  canUseSDK: boolean;
}

/**
 * Get current wallet status
 */
export function useWalletStatus(): WalletStatus {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  
  const isCorrectNetwork = chainId ? isBaseSepolia(chainId) : false;
  const needsNetworkSwitch = isConnected && !isCorrectNetwork;
  const canUseSDK = isConnected && isCorrectNetwork;
  
  return {
    isConnected,
    address,
    chainId,
    isCorrectNetwork,
    needsNetworkSwitch,
    canUseSDK,
  };
}

/**
 * Wallet validation hook with actions
 */
export function useWalletValidation() {
  const { openConnectModal } = useConnectModal();
  const { disconnect } = useDisconnect();
  const walletStatus = useWalletStatus();
  
  const connectWallet = () => {
    if (openConnectModal) {
      openConnectModal();
    } else {
      notifications.show({
        title: 'Connection Error',
        message: 'Unable to open wallet connection modal',
        color: 'red',
      });
    }
  };
  
  const disconnectWallet = () => {
    disconnect();
    notifications.show({
      title: 'Wallet Disconnected',
      message: 'Your wallet has been disconnected',
      color: 'blue',
    });
  };
  
  const switchToCorrectNetwork = async () => {
    if (walletStatus.chainId) {
      return await validateAndSwitchNetwork(walletStatus.chainId);
    }
    return false;
  };
  
  const validateWalletForSDK = async (): Promise<boolean> => {
    consoleLog('Validating wallet for ODude SDK', walletStatus);
    
    // Check if wallet is connected
    if (!walletStatus.isConnected) {
      notifications.show({
        title: 'Wallet Required',
        message: 'Please connect your wallet to use ODude SDK features',
        color: 'orange',
      });
      return false;
    }
    
    // Check if on correct network
    if (!walletStatus.isCorrectNetwork) {
      notifications.show({
        title: 'Wrong Network',
        message: 'Please switch to Base Sepolia network to use ODude SDK',
        color: 'orange',
      });
      
      // Attempt to switch automatically
      const switched = await switchToCorrectNetwork();
      return switched;
    }
    
    consoleLog('Wallet validation passed for ODude SDK');
    return true;
  };
  
  return {
    ...walletStatus,
    connectWallet,
    disconnectWallet,
    switchToCorrectNetwork,
    validateWalletForSDK,
  };
}

/**
 * Show wallet connection requirement notification
 */
export function showWalletRequiredNotification(action: string = 'perform this action') {
  notifications.show({
    id: 'wallet-required',
    title: 'Wallet Connection Required',
    message: `Please connect your wallet to ${action}`,
    color: 'orange',
    autoClose: false,
    withCloseButton: true,
  });
}

/**
 * Show network switch requirement notification
 */
export function showNetworkSwitchNotification() {
  notifications.show({
    id: 'network-switch-required',
    title: 'Network Switch Required',
    message: 'Please switch to Base Sepolia network to use ODude features',
    color: 'orange',
    autoClose: false,
    withCloseButton: true,
  });
}

/**
 * Hide wallet-related notifications
 */
export function hideWalletNotifications() {
  notifications.hide('wallet-required');
  notifications.hide('network-switch-required');
}

/**
 * Wallet guard component props
 */
export interface WalletGuardProps {
  children: React.ReactNode;
  requireWallet?: boolean;
  requireCorrectNetwork?: boolean;
  fallback?: React.ReactNode;
  onWalletRequired?: () => void;
  onNetworkSwitchRequired?: () => void;
}

/**
 * Higher-order component for wallet validation
 */
export function withWalletValidation<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    requireWallet?: boolean;
    requireCorrectNetwork?: boolean;
  } = {}
) {
  return function WalletValidatedComponent(props: P) {
    const walletValidation = useWalletValidation();
    
    // Check wallet connection requirement
    if (options.requireWallet && !walletValidation.isConnected) {
      return (
        <div>
          <p>Wallet connection required</p>
          <button onClick={walletValidation.connectWallet}>
            Connect Wallet
          </button>
        </div>
      );
    }
    
    // Check network requirement
    if (options.requireCorrectNetwork && !walletValidation.isCorrectNetwork) {
      return (
        <div>
          <p>Please switch to Base Sepolia network</p>
          <button onClick={walletValidation.switchToCorrectNetwork}>
            Switch Network
          </button>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
}

/**
 * Wallet connection status component data
 */
export interface WalletConnectionInfo {
  status: 'disconnected' | 'connected' | 'wrong-network' | 'ready';
  message: string;
  action?: {
    label: string;
    handler: () => void;
  };
}

export function getWalletConnectionInfo(walletStatus: WalletStatus): WalletConnectionInfo {
  if (!walletStatus.isConnected) {
    return {
      status: 'disconnected',
      message: 'Wallet not connected',
    };
  }
  
  if (!walletStatus.isCorrectNetwork) {
    return {
      status: 'wrong-network',
      message: `Connected to wrong network. Switch to Base Sepolia (Chain ID: ${ODUDE_SDK_CONFIG.CHAIN_ID})`,
    };
  }
  
  return {
    status: 'ready',
    message: `Connected to Base Sepolia with address ${walletStatus.address}`,
  };
}

/**
 * Validate wallet before executing SDK operations
 */
export async function validateWalletForOperation(
  operation: string,
  walletValidation: ReturnType<typeof useWalletValidation>
): Promise<boolean> {
  consoleLog(`Validating wallet for operation: ${operation}`);
  
  if (!walletValidation.isConnected) {
    showWalletRequiredNotification(operation);
    return false;
  }
  
  if (!walletValidation.isCorrectNetwork) {
    showNetworkSwitchNotification();
    
    // Attempt automatic network switch
    const switched = await walletValidation.switchToCorrectNetwork();
    if (!switched) {
      return false;
    }
  }
  
  hideWalletNotifications();
  consoleLog(`Wallet validation passed for operation: ${operation}`);
  return true;
}

/**
 * Wallet status badge data
 */
export interface WalletBadgeData {
  color: string;
  label: string;
  icon?: string;
}

export function getWalletBadgeData(walletStatus: WalletStatus): WalletBadgeData {
  if (!walletStatus.isConnected) {
    return {
      color: 'red',
      label: 'Disconnected',
      icon: 'wallet-off',
    };
  }
  
  if (!walletStatus.isCorrectNetwork) {
    return {
      color: 'orange',
      label: 'Wrong Network',
      icon: 'network',
    };
  }
  
  return {
    color: 'green',
    label: 'Connected',
    icon: 'wallet',
  };
}

/**
 * Initialize wallet validation on app start
 */
export function initializeWalletValidation() {
  consoleLog('Initializing wallet validation system');
  
  // Any initialization logic can go here
  // For now, just log that the system is ready
  consoleLog('Wallet validation system initialized');
}

// Export constants
export const REQUIRED_CHAIN_ID = ODUDE_SDK_CONFIG.CHAIN_ID;
export const REQUIRED_NETWORK_NAME = 'Base Sepolia';
