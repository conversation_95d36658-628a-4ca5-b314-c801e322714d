// src/lib/odude-sdk-integration.ts
// Integration layer for odude-sdk with the existing application interface

import ODudeSDK from 'odude-sdk';
import { BrowserProvider } from 'ethers';
import { ODUDE_SDK_CONFIG, consoleLog, consoleError } from './config';
import type { NetworkConfig, ContractCallResult } from './library';

// SDK instance (singleton)
let sdkInstance: ODudeSDK | null = null;

/**
 * Initialize ODude SDK with Base Sepolia configuration
 */
export function initializeSDK(provider?: any): ODudeSDK {
  consoleLog('Initializing ODude SDK', { network: ODUDE_SDK_CONFIG.NETWORK });
  
  try {
    // Create SDK instance
    const config: any = {
      rpcUrl: ODUDE_SDK_CONFIG.RPC_URL,
    };
    
    // Add provider if available (for wallet interactions)
    if (provider) {
      config.provider = new BrowserProvider(provider);
    }
    
    const sdk = new ODudeSDK(config);
    
    // Connect to Base Sepolia network
    sdk.connectNetwork(ODUDE_SDK_CONFIG.NETWORK);
    
    consoleLog('ODude SDK initialized successfully', {
      network: ODUDE_SDK_CONFIG.NETWORK,
      chainId: ODUDE_SDK_CONFIG.CHAIN_ID,
      contracts: ODUDE_SDK_CONFIG.CONTRACTS
    });
    
    sdkInstance = sdk;
    return sdk;
  } catch (error) {
    consoleError('Failed to initialize ODude SDK', error);
    throw error;
  }
}

/**
 * Get or create SDK instance
 */
export function getSDK(provider?: any): ODudeSDK {
  if (!sdkInstance) {
    return initializeSDK(provider);
  }
  
  // Update provider if provided
  if (provider && !sdkInstance.signer) {
    try {
      const browserProvider = new BrowserProvider(provider);
      sdkInstance.connectSigner(browserProvider.getSigner());
    } catch (error) {
      consoleError('Failed to connect signer to SDK', error);
    }
  }
  
  return sdkInstance;
}

/**
 * Connect wallet signer to SDK for write operations
 */
export async function connectWalletToSDK(provider: any): Promise<ODudeSDK> {
  consoleLog('Connecting wallet to ODude SDK');
  
  try {
    const browserProvider = new BrowserProvider(provider);
    const signer = await browserProvider.getSigner();
    
    const sdk = getSDK();
    sdk.connectSigner(signer);
    
    consoleLog('Wallet connected to ODude SDK successfully');
    return sdk;
  } catch (error) {
    consoleError('Failed to connect wallet to ODude SDK', error);
    throw error;
  }
}

/**
 * Get domain ID by name using ODude SDK
 */
export async function getDomainId(domainName: string): Promise<number | null> {
  consoleLog('getDomainId called via SDK', { domainName });
  
  try {
    const sdk = getSDK();
    const tokenId = await sdk.registry.getTokenId(domainName);
    return Number(tokenId);
  } catch (error) {
    consoleError('Error getting domain ID via SDK', error);
    return null;
  }
}

/**
 * Get domain owner by token ID using ODude SDK
 */
export async function getDomainOwner(tokenId: number): Promise<string | null> {
  consoleLog('getDomainOwner called via SDK', { tokenId });
  
  try {
    const sdk = getSDK();
    const owner = await sdk.registry.ownerOf(tokenId);
    return owner;
  } catch (error) {
    consoleError('Error getting domain owner via SDK', error);
    return null;
  }
}

/**
 * Get domain owner by name using ODude SDK
 */
export async function getDomainOwnerByName(domainName: string): Promise<string | null> {
  consoleLog('getDomainOwnerByName called via SDK', { domainName });
  
  try {
    const sdk = getSDK();
    const owner = await sdk.getOwner(domainName);
    return owner;
  } catch (error) {
    consoleError('Error getting domain owner by name via SDK', error);
    return null;
  }
}

/**
 * Check if domain/name exists using ODude SDK
 */
export async function checkDomainExists(domainName: string): Promise<boolean> {
  consoleLog('checkDomainExists called via SDK', { domainName });
  
  try {
    const sdk = getSDK();
    const exists = await sdk.resolver.nameExists(domainName);
    return exists;
  } catch (error) {
    consoleError('Error checking domain existence via SDK', error);
    return false;
  }
}

/**
 * Check if domain is available (opposite of exists)
 */
export async function isDomainAvailable(domainName: string): Promise<boolean> {
  const exists = await checkDomainExists(domainName);
  return !exists;
}

/**
 * Get TLD price from contract
 */
export async function getTLDPrice(): Promise<string> {
  consoleLog('getTLDPrice called via SDK');
  
  try {
    const sdk = getSDK();
    const price = await sdk.tld.getBaseTLDPrice();
    return price.toString();
  } catch (error) {
    consoleError('Error getting TLD price via SDK', error);
    return ODUDE_SDK_CONFIG.BASE_TLD_PRICE;
  }
}

/**
 * Get default commission from TLD contract
 */
export async function getDefaultCommission(): Promise<number> {
  consoleLog('getDefaultCommission called via SDK');
  
  try {
    const sdk = getSDK();
    const commission = await sdk.tld.getDefaultCommission();
    return Number(commission);
  } catch (error) {
    consoleError('Error getting default commission via SDK', error);
    return ODUDE_SDK_CONFIG.DEFAULT_COMMISSION;
  }
}

/**
 * Get comprehensive name information using ODude SDK
 */
export async function getNameInfo(domainName: string): Promise<any> {
  consoleLog('getNameInfo called via SDK', { domainName });
  
  try {
    const sdk = getSDK();
    const nameInfo = await sdk.getNameInfo(domainName);
    return nameInfo;
  } catch (error) {
    consoleError('Error getting name info via SDK', error);
    return null;
  }
}

/**
 * Get name details using ODude SDK
 */
export async function getNameDetails(domainName: string): Promise<any> {
  consoleLog('getNameDetails called via SDK', { domainName });
  
  try {
    const sdk = getSDK();
    const details = await sdk.getNameDetails(domainName);
    return details;
  } catch (error) {
    consoleError('Error getting name details via SDK', error);
    return null;
  }
}

/**
 * Resolve name to address using ODude SDK
 */
export async function resolveName(domainName: string): Promise<string | null> {
  consoleLog('resolveName called via SDK', { domainName });
  
  try {
    const sdk = getSDK();
    const address = await sdk.resolve(domainName);
    return address;
  } catch (error) {
    consoleError('Error resolving name via SDK', error);
    return null;
  }
}

/**
 * Reverse resolve address to name using ODude SDK
 */
export async function reverseResolve(address: string): Promise<string | null> {
  consoleLog('reverseResolve called via SDK', { address });
  
  try {
    const sdk = getSDK();
    const name = await sdk.reverse(address);
    return name;
  } catch (error) {
    consoleError('Error reverse resolving address via SDK', error);
    return null;
  }
}

/**
 * Get airdrop information for an address
 */
export async function getAirdropInfo(address: string): Promise<any> {
  consoleLog('getAirdropInfo called via SDK', { address });
  
  try {
    const sdk = getSDK();
    const airdropInfo = await sdk.getAirdropInfo(address);
    return airdropInfo;
  } catch (error) {
    consoleError('Error getting airdrop info via SDK', error);
    return null;
  }
}

/**
 * Get all names owned by an address
 */
export async function getNamesList(address: string): Promise<Array<{tokenId: string, name: string}>> {
  consoleLog('getNamesList called via SDK', { address });
  
  try {
    const sdk = getSDK();
    const names = await sdk.getNamesList(address);
    return names;
  } catch (error) {
    consoleError('Error getting names list via SDK', error);
    return [];
  }
}

/**
 * Get total supply of names
 */
export async function getTotalSupply(): Promise<number> {
  consoleLog('getTotalSupply called via SDK');
  
  try {
    const sdk = getSDK();
    const totalSupply = await sdk.registry.totalSupply();
    return Number(totalSupply);
  } catch (error) {
    consoleError('Error getting total supply via SDK', error);
    return 0;
  }
}

/**
 * Get contract information
 */
export async function getContractInfo(): Promise<any> {
  consoleLog('getContractInfo called via SDK');
  
  try {
    const sdk = getSDK();
    const [registryName, registrySymbol, totalSupply, baseTLDPrice, defaultCommission] = await Promise.all([
      sdk.registry.name(),
      sdk.registry.symbol(),
      sdk.registry.totalSupply(),
      sdk.tld.getBaseTLDPrice(),
      sdk.tld.getDefaultCommission()
    ]);
    
    return {
      registry: {
        name: registryName,
        symbol: registrySymbol,
        totalSupply: totalSupply.toString(),
        address: sdk.registry.address
      },
      resolver: {
        address: sdk.resolver.address
      },
      tld: {
        address: sdk.tld.address,
        baseTLDPrice: baseTLDPrice.toString(),
        defaultCommission: defaultCommission.toString()
      },
      rwairdrop: {
        address: sdk.rwairdrop.address
      }
    };
  } catch (error) {
    consoleError('Error getting contract info via SDK', error);
    return null;
  }
}

// Export the SDK configuration for external use
export { ODUDE_SDK_CONFIG };
