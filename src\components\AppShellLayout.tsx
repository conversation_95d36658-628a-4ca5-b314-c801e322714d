'use client';

import { useEffect } from 'react';
import { useAccount, useChainId } from 'wagmi';
import {
  AppShell,
  Burger,
  Group,
  Text,
  Title,
  Stack,
  NavLink,
  Box,
  ThemeIcon,
  Divider,
  Badge,
  Alert,
} from '@mantine/core';
import {
  IconDashboard,
  IconWallet,
  IconSettings,
  IconUsers,
  IconChartBar,
  IconShoppingCart,
  IconInfoCircle,
  IconTestPipe,
  IconNetwork,
} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { consoleLog } from '../lib/config';
import { useWalletValidation } from '../lib/wallet-validation';
import { initializeNetworkValidation } from '../lib/network-validation';
import WalletStatus from './WalletStatus';

interface AppShellLayoutProps {
  children: React.ReactNode;
  onWalletDisconnect?: () => void;
}

export default function AppShellLayout({ children, onWalletDisconnect }: AppShellLayoutProps) {
  const [opened, { toggle, close }] = useDisclosure();
  const { address, isConnected, isDisconnected } = useAccount();
  const chainId = useChainId();
  const walletValidation = useWalletValidation();

  // Initialize network validation
  useEffect(() => {
    if (chainId) {
      initializeNetworkValidation(chainId);
    }
  }, [chainId]);

  // Log wallet state changes in AppShell
  useEffect(() => {
    consoleLog('AppShellLayout - Wallet state changed:', {
      address,
      isConnected,
      isDisconnected,
      chainId,
      isCorrectNetwork: walletValidation.isCorrectNetwork,
    });

    if (isDisconnected) {
      onWalletDisconnect?.();
    }
  }, [address, isConnected, isDisconnected, chainId, walletValidation.isCorrectNetwork, onWalletDisconnect]);

  const handleLogout = () => {
    consoleLog('AppShellLayout - Logout triggered');
    close(); // Close mobile navbar
    onWalletDisconnect?.();
  };

  const navigationItems = [
    { icon: IconDashboard, label: 'Dashboard', href: '/' },
    { icon: IconInfoCircle, label: 'Domain Info', href: '/info' },
    { icon: IconTestPipe, label: 'SDK Test', href: '/test-sdk' },
    { icon: IconShoppingCart, label: 'Orders', href: '/orders' },
    { icon: IconUsers, label: 'Customers', href: '/customers' },
    { icon: IconChartBar, label: 'Analytics', href: '/analytics' },
    { icon: IconWallet, label: 'Wallet', href: '/wallet' },
    { icon: IconSettings, label: 'Settings', href: '/settings' },
  ];

  return (
    <AppShell
      header={{ height: { base: 60, md: 70, lg: 80 } }}
      navbar={{
        width: { base: 200, md: 250, lg: 280 },
        breakpoint: 'sm',
        collapsed: { mobile: !opened },
      }}
      padding="md"
    >
      <AppShell.Header>
        <Group h="100%" px="md" justify="space-between">
          <Group>
            <Burger opened={opened} onClick={toggle} hiddenFrom="sm" size="sm" />
            <Group gap="xs">
              <ThemeIcon size={32} radius="md" variant="gradient" gradient={{ from: 'blue', to: 'cyan' }}>
                <IconWallet size={20} />
              </ThemeIcon>
              <Box>
                <Title order={4} c="white">
                  ODude Dealer
                </Title>
                <Text size="xs" c="dimmed">
                  Top Level Name Management DApp
                </Text>
              </Box>
            </Group>
          </Group>

          {/* Wallet and Network Status in Header */}
          <Group>
            {isConnected && chainId && (
              <Badge
                color={walletValidation.isCorrectNetwork ? 'green' : 'orange'}
                variant="light"
                leftSection={<IconNetwork size={12} />}
              >
                {walletValidation.isCorrectNetwork ? 'Base Sepolia' : `Chain ${chainId}`}
              </Badge>
            )}
            {isConnected && (
              <WalletStatus onLogout={handleLogout} />
            )}
          </Group>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar p="md">
        <Stack gap="xs">
          <Box mb="md">
            <Text size="sm" fw={500} c="dimmed" tt="uppercase" mb="xs">
              Navigation
            </Text>
            <Divider />
          </Box>

          {navigationItems.map((item) => (
            <NavLink
              key={item.label}
              href={item.href}
              label={item.label}
              leftSection={<item.icon size={18} />}
              variant="light"
              style={{
                borderRadius: '8px',
              }}
            />
          ))}

          <Box mt="auto" pt="md">
            <Divider mb="md" />
            <Text size="xs" c="dimmed" ta="center">
              {isConnected ? (
                <>Connected to {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Wallet'}</>
              ) : (
                'Wallet not connected'
              )}
            </Text>
          </Box>
        </Stack>
      </AppShell.Navbar>

      <AppShell.Main>
        {children}
      </AppShell.Main>
    </AppShell>
  );
}
