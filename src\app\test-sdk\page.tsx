'use client';

import { useState, useEffect } from 'react';
import { useAccount, useChainId } from 'wagmi';
import {
  Container,
  Title,
  Card,
  Text,
  Button,
  Group,
  Badge,
  Stack,
  Alert,
  Loader,
  Divider,
  TextInput,
  Grid,
  Paper,
  Tabs,
  JsonInput,
  Code,
  ScrollArea,
  ActionIcon,
  Tooltip,
} from '@mantine/core';
import {
  IconInfoCircle,
  IconWallet,
  IconNetwork,
  IconSearch,
  IconRefresh,
  IconCopy,
  IconCheck,
  IconX,
  IconDatabase,
  IconGift,
  IconDns,
  IconSettings,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import {
  initializeODudeSDK,
  connectWalletToODudeSDK,
  getContractInfo,
  checkDomainExists,
  isDomainAvailable,
  getNameInfo,
  getNameDetails,
  resolveName,
  reverseResolve,
  getAirdropInfo,
  getNamesList,
  getTotalSupply,
  formatAddress,
} from '../../lib/library';
import { validateAndSwitchNetwork, useNetworkValidation } from '../../lib/network-validation';
import AppShellLayout from '../../components/AppShellLayout';
import { ODUDE_SDK_CONFIG, consoleLog, consoleError } from '../../lib/config';

interface TestResult {
  success: boolean;
  data?: any;
  error?: string;
  timestamp: Date;
}

interface ContractInfo {
  registry?: any;
  resolver?: any;
  tld?: any;
  rwairdrop?: any;
}

export default function TestSDKPage() {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  
  // State management
  const [loading, setLoading] = useState(false);
  const [contractInfo, setContractInfo] = useState<ContractInfo>({});
  const [testResults, setTestResults] = useState<Record<string, TestResult>>({});
  const [testInput, setTestInput] = useState('');
  const [addressInput, setAddressInput] = useState('');
  const [sdkInitialized, setSdkInitialized] = useState(false);
  
  // Network validation
  const networkValidation = useNetworkValidation(chainId || 0);
  
  const handleWalletDisconnect = () => {
    consoleLog('TestSDK - Wallet disconnected via AppShell');
    setSdkInitialized(false);
    setContractInfo({});
    setTestResults({});
  };

  // Initialize SDK when wallet connects
  useEffect(() => {
    if (isConnected && networkValidation.isCorrectNetwork && !sdkInitialized) {
      initializeSDK();
    }
  }, [isConnected, networkValidation.isCorrectNetwork, sdkInitialized]);

  // Initialize SDK
  const initializeSDK = async () => {
    setLoading(true);
    try {
      consoleLog('Initializing ODude SDK for testing');
      
      // Initialize SDK
      const sdk = initializeODudeSDK();
      
      // Connect wallet if available
      if (window.ethereum) {
        await connectWalletToODudeSDK(window.ethereum);
      }
      
      setSdkInitialized(true);
      
      // Load contract information
      await loadContractInfo();
      
      notifications.show({
        title: 'SDK Initialized',
        message: 'ODude SDK has been successfully initialized',
        color: 'green',
      });
      
    } catch (error) {
      consoleError('Failed to initialize SDK', error);
      notifications.show({
        title: 'SDK Initialization Failed',
        message: 'Failed to initialize ODude SDK. Check console for details.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  // Load contract information
  const loadContractInfo = async () => {
    try {
      const info = await getContractInfo();
      setContractInfo(info || {});
      
      addTestResult('contractInfo', {
        success: true,
        data: info,
        timestamp: new Date(),
      });
    } catch (error) {
      consoleError('Failed to load contract info', error);
      addTestResult('contractInfo', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      });
    }
  };

  // Add test result
  const addTestResult = (testName: string, result: TestResult) => {
    setTestResults(prev => ({
      ...prev,
      [testName]: result,
    }));
  };

  // Test functions
  const testDomainExists = async () => {
    if (!testInput.trim()) {
      notifications.show({
        title: 'Input Required',
        message: 'Please enter a domain name to test',
        color: 'orange',
      });
      return;
    }

    setLoading(true);
    try {
      const exists = await checkDomainExists(testInput.trim());
      addTestResult('domainExists', {
        success: true,
        data: { domain: testInput.trim(), exists },
        timestamp: new Date(),
      });
    } catch (error) {
      addTestResult('domainExists', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      });
    } finally {
      setLoading(false);
    }
  };

  const testDomainAvailable = async () => {
    if (!testInput.trim()) {
      notifications.show({
        title: 'Input Required',
        message: 'Please enter a domain name to test',
        color: 'orange',
      });
      return;
    }

    setLoading(true);
    try {
      const available = await isDomainAvailable(testInput.trim());
      addTestResult('domainAvailable', {
        success: true,
        data: { domain: testInput.trim(), available },
        timestamp: new Date(),
      });
    } catch (error) {
      addTestResult('domainAvailable', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      });
    } finally {
      setLoading(false);
    }
  };

  const testNameInfo = async () => {
    if (!testInput.trim()) {
      notifications.show({
        title: 'Input Required',
        message: 'Please enter a domain name to test',
        color: 'orange',
      });
      return;
    }

    setLoading(true);
    try {
      const info = await getNameInfo(testInput.trim());
      addTestResult('nameInfo', {
        success: true,
        data: info,
        timestamp: new Date(),
      });
    } catch (error) {
      addTestResult('nameInfo', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      });
    } finally {
      setLoading(false);
    }
  };

  const testNameDetails = async () => {
    if (!testInput.trim()) {
      notifications.show({
        title: 'Input Required',
        message: 'Please enter a domain name to test',
        color: 'orange',
      });
      return;
    }

    setLoading(true);
    try {
      const details = await getNameDetails(testInput.trim());
      addTestResult('nameDetails', {
        success: true,
        data: details,
        timestamp: new Date(),
      });
    } catch (error) {
      addTestResult('nameDetails', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      });
    } finally {
      setLoading(false);
    }
  };

  const testResolveName = async () => {
    if (!testInput.trim()) {
      notifications.show({
        title: 'Input Required',
        message: 'Please enter a domain name to test',
        color: 'orange',
      });
      return;
    }

    setLoading(true);
    try {
      const resolvedAddress = await resolveName(testInput.trim());
      addTestResult('resolveName', {
        success: true,
        data: { domain: testInput.trim(), resolvedAddress },
        timestamp: new Date(),
      });
    } catch (error) {
      addTestResult('resolveName', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      });
    } finally {
      setLoading(false);
    }
  };

  const testReverseResolve = async () => {
    const inputAddress = addressInput.trim() || address;
    if (!inputAddress) {
      notifications.show({
        title: 'Input Required',
        message: 'Please enter an address or connect your wallet',
        color: 'orange',
      });
      return;
    }

    setLoading(true);
    try {
      const name = await reverseResolve(inputAddress);
      addTestResult('reverseResolve', {
        success: true,
        data: { address: inputAddress, name },
        timestamp: new Date(),
      });
    } catch (error) {
      addTestResult('reverseResolve', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      });
    } finally {
      setLoading(false);
    }
  };

  const testAirdropInfo = async () => {
    const inputAddress = addressInput.trim() || address;
    if (!inputAddress) {
      notifications.show({
        title: 'Input Required',
        message: 'Please enter an address or connect your wallet',
        color: 'orange',
      });
      return;
    }

    setLoading(true);
    try {
      const airdropInfo = await getAirdropInfo(inputAddress);
      addTestResult('airdropInfo', {
        success: true,
        data: airdropInfo,
        timestamp: new Date(),
      });
    } catch (error) {
      addTestResult('airdropInfo', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      });
    } finally {
      setLoading(false);
    }
  };

  const testNamesList = async () => {
    const inputAddress = addressInput.trim() || address;
    if (!inputAddress) {
      notifications.show({
        title: 'Input Required',
        message: 'Please enter an address or connect your wallet',
        color: 'orange',
      });
      return;
    }

    setLoading(true);
    try {
      const names = await getNamesList(inputAddress);
      addTestResult('namesList', {
        success: true,
        data: names,
        timestamp: new Date(),
      });
    } catch (error) {
      addTestResult('namesList', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      });
    } finally {
      setLoading(false);
    }
  };

  const testTotalSupply = async () => {
    setLoading(true);
    try {
      const totalSupply = await getTotalSupply();
      addTestResult('totalSupply', {
        success: true,
        data: { totalSupply },
        timestamp: new Date(),
      });
    } catch (error) {
      addTestResult('totalSupply', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      });
    } finally {
      setLoading(false);
    }
  };

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    notifications.show({
      title: 'Copied',
      message: 'Content copied to clipboard',
      color: 'green',
    });
  };

  // Clear all test results
  const clearResults = () => {
    setTestResults({});
    notifications.show({
      title: 'Cleared',
      message: 'All test results have been cleared',
      color: 'blue',
    });
  };

  return (
    <AppShellLayout onWalletDisconnect={handleWalletDisconnect}>
      <Container size="xl" py="xl">
        <Stack gap="xl">
          {/* Header */}
          <div>
            <Title order={1} mb="xs">
              ODude SDK Test Page
            </Title>
            <Text c="dimmed">
              Comprehensive testing interface for all ODude SDK functionality on Base Sepolia
            </Text>
          </div>

          {/* Network Status */}
          {!networkValidation.isCorrectNetwork && (
            <Alert icon={<IconInfoCircle size={16} />} color="orange" variant="light">
              <Group justify="space-between">
                <div>
                  <Text fw={500}>Wrong Network</Text>
                  <Text size="sm">
                    Connected to {networkValidation.currentNetworkName}. Switch to {networkValidation.requiredNetworkName} to test ODude SDK.
                  </Text>
                </div>
                <Button
                  size="sm"
                  onClick={networkValidation.switchNetwork}
                >
                  Switch Network
                </Button>
              </Group>
            </Alert>
          )}

          {/* SDK Status */}
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Group justify="space-between" mb="md">
              <Title order={3}>SDK Status</Title>
              <Group>
                <Badge color={sdkInitialized ? 'green' : 'red'}>
                  {sdkInitialized ? 'Initialized' : 'Not Initialized'}
                </Badge>
                <Badge color={networkValidation.isCorrectNetwork ? 'green' : 'orange'}>
                  {networkValidation.currentNetworkName}
                </Badge>
              </Group>
            </Group>
            
            <Grid>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">Network</Text>
                <Text fw={500}>{ODUDE_SDK_CONFIG.NETWORK}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">Chain ID</Text>
                <Text fw={500}>{ODUDE_SDK_CONFIG.CHAIN_ID}</Text>
              </Grid.Col>
              <Grid.Col span={12}>
                <Text size="sm" c="dimmed">RPC URL</Text>
                <Text fw={500} style={{ wordBreak: 'break-all' }}>{ODUDE_SDK_CONFIG.RPC_URL}</Text>
              </Grid.Col>
            </Grid>

            {!sdkInitialized && networkValidation.isCorrectNetwork && (
              <Button
                mt="md"
                onClick={initializeSDK}
                loading={loading}
                leftSection={<IconSettings size={16} />}
              >
                Initialize SDK
              </Button>
            )}
          </Card>

          {/* Contract Information */}
          {sdkInitialized && (
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Title order={3}>Contract Information</Title>
                <Button
                  size="sm"
                  variant="light"
                  onClick={loadContractInfo}
                  leftSection={<IconRefresh size={16} />}
                >
                  Refresh
                </Button>
              </Group>

              <Grid>
                {contractInfo.registry && (
                  <>
                    <Grid.Col span={6}>
                      <Paper p="md" withBorder>
                        <Text size="sm" c="dimmed">Registry Contract</Text>
                        <Text fw={500} size="sm" style={{ wordBreak: 'break-all' }}>
                          {contractInfo.registry.address}
                        </Text>
                        <Text size="xs" c="dimmed">
                          {contractInfo.registry.name} ({contractInfo.registry.symbol})
                        </Text>
                        <Text size="xs" c="dimmed">
                          Total Supply: {contractInfo.registry.totalSupply}
                        </Text>
                      </Paper>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Paper p="md" withBorder>
                        <Text size="sm" c="dimmed">Resolver Contract</Text>
                        <Text fw={500} size="sm" style={{ wordBreak: 'break-all' }}>
                          {contractInfo.resolver.address}
                        </Text>
                      </Paper>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Paper p="md" withBorder>
                        <Text size="sm" c="dimmed">TLD Contract</Text>
                        <Text fw={500} size="sm" style={{ wordBreak: 'break-all' }}>
                          {contractInfo.tld.address}
                        </Text>
                        <Text size="xs" c="dimmed">
                          Base Price: {contractInfo.tld.baseTLDPrice} ETH
                        </Text>
                        <Text size="xs" c="dimmed">
                          Commission: {contractInfo.tld.defaultCommission}%
                        </Text>
                      </Paper>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Paper p="md" withBorder>
                        <Text size="sm" c="dimmed">RWAirdrop Contract</Text>
                        <Text fw={500} size="sm" style={{ wordBreak: 'break-all' }}>
                          {contractInfo.rwairdrop.address}
                        </Text>
                      </Paper>
                    </Grid.Col>
                  </>
                )}
              </Grid>
            </Card>
          )}

          {/* Test Interface */}
          {sdkInitialized && (
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="md">
                <Title order={3}>Test Interface</Title>
                <Button
                  size="sm"
                  variant="light"
                  color="red"
                  onClick={clearResults}
                  leftSection={<IconX size={16} />}
                >
                  Clear Results
                </Button>
              </Group>

              <Tabs defaultValue="domain" keepMounted={false}>
                <Tabs.List>
                  <Tabs.Tab value="domain" leftSection={<IconDns size={16} />}>
                    Domain Tests
                  </Tabs.Tab>
                  <Tabs.Tab value="address" leftSection={<IconWallet size={16} />}>
                    Address Tests
                  </Tabs.Tab>
                  <Tabs.Tab value="contract" leftSection={<IconDatabase size={16} />}>
                    Contract Tests
                  </Tabs.Tab>
                  <Tabs.Tab value="airdrop" leftSection={<IconGift size={16} />}>
                    Airdrop Tests
                  </Tabs.Tab>
                </Tabs.List>

                <Tabs.Panel value="domain" pt="md">
                  <Stack gap="md">
                    <TextInput
                      label="Domain Name"
                      placeholder="Enter domain name (e.g., bitcoin@crypto)"
                      value={testInput}
                      onChange={(e) => setTestInput(e.currentTarget.value)}
                      leftSection={<IconSearch size={16} />}
                    />

                    <Group>
                      <Button
                        onClick={testDomainExists}
                        loading={loading}
                        leftSection={<IconSearch size={16} />}
                      >
                        Check Exists
                      </Button>
                      <Button
                        onClick={testDomainAvailable}
                        loading={loading}
                        leftSection={<IconSearch size={16} />}
                      >
                        Check Available
                      </Button>
                      <Button
                        onClick={testNameInfo}
                        loading={loading}
                        leftSection={<IconInfoCircle size={16} />}
                      >
                        Get Name Info
                      </Button>
                      <Button
                        onClick={testNameDetails}
                        loading={loading}
                        leftSection={<IconInfoCircle size={16} />}
                      >
                        Get Name Details
                      </Button>
                      <Button
                        onClick={testResolveName}
                        loading={loading}
                        leftSection={<IconDns size={16} />}
                      >
                        Resolve Name
                      </Button>
                    </Group>
                  </Stack>
                </Tabs.Panel>

                <Tabs.Panel value="address" pt="md">
                  <Stack gap="md">
                    <TextInput
                      label="Address"
                      placeholder={`Enter address or leave empty to use connected wallet: ${formatAddress(address || '')}`}
                      value={addressInput}
                      onChange={(e) => setAddressInput(e.currentTarget.value)}
                      leftSection={<IconWallet size={16} />}
                    />

                    <Group>
                      <Button
                        onClick={testReverseResolve}
                        loading={loading}
                        leftSection={<IconDns size={16} />}
                      >
                        Reverse Resolve
                      </Button>
                      <Button
                        onClick={testNamesList}
                        loading={loading}
                        leftSection={<IconDatabase size={16} />}
                      >
                        Get Names List
                      </Button>
                    </Group>
                  </Stack>
                </Tabs.Panel>

                <Tabs.Panel value="contract" pt="md">
                  <Stack gap="md">
                    <Group>
                      <Button
                        onClick={testTotalSupply}
                        loading={loading}
                        leftSection={<IconDatabase size={16} />}
                      >
                        Get Total Supply
                      </Button>
                      <Button
                        onClick={loadContractInfo}
                        loading={loading}
                        leftSection={<IconRefresh size={16} />}
                      >
                        Refresh Contract Info
                      </Button>
                    </Group>
                  </Stack>
                </Tabs.Panel>

                <Tabs.Panel value="airdrop" pt="md">
                  <Stack gap="md">
                    <TextInput
                      label="Address"
                      placeholder={`Enter address or leave empty to use connected wallet: ${formatAddress(address || '')}`}
                      value={addressInput}
                      onChange={(e) => setAddressInput(e.currentTarget.value)}
                      leftSection={<IconWallet size={16} />}
                    />

                    <Group>
                      <Button
                        onClick={testAirdropInfo}
                        loading={loading}
                        leftSection={<IconGift size={16} />}
                      >
                        Get Airdrop Info
                      </Button>
                    </Group>
                  </Stack>
                </Tabs.Panel>
              </Tabs>
            </Card>
          )}

          {/* Test Results */}
          {Object.keys(testResults).length > 0 && (
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Title order={3} mb="md">Test Results</Title>

              <ScrollArea h={400}>
                <Stack gap="md">
                  {Object.entries(testResults).map(([testName, result]) => (
                    <Paper key={testName} p="md" withBorder>
                      <Group justify="space-between" mb="sm">
                        <Group>
                          <Badge color={result.success ? 'green' : 'red'}>
                            {result.success ? <IconCheck size={12} /> : <IconX size={12} />}
                          </Badge>
                          <Text fw={500}>{testName}</Text>
                        </Group>
                        <Group>
                          <Text size="xs" c="dimmed">
                            {result.timestamp.toLocaleTimeString()}
                          </Text>
                          <Tooltip label="Copy Result">
                            <ActionIcon
                              size="sm"
                              variant="light"
                              onClick={() => copyToClipboard(JSON.stringify(result, null, 2))}
                            >
                              <IconCopy size={12} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Group>

                      {result.success && result.data && (
                        <JsonInput
                          value={JSON.stringify(result.data, null, 2)}
                          readOnly
                          autosize
                          minRows={2}
                          maxRows={10}
                        />
                      )}

                      {!result.success && result.error && (
                        <Code color="red" block>
                          {result.error}
                        </Code>
                      )}
                    </Paper>
                  ))}
                </Stack>
              </ScrollArea>
            </Card>
          )}
        </Stack>
      </Container>
    </AppShellLayout>
  );
}
