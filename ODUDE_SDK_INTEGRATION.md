# ODude SDK Integration Guide

This document provides comprehensive information about the ODude SDK integration in the ODude Dealer application.

## Overview

The ODude Dealer application has been fully integrated with the `odude-sdk` package, replacing the previous `@odude/oduderesolve` package. The integration provides seamless access to all ODude smart contracts on Base Sepolia network.

## Architecture

### Integration Layer

The integration is structured in multiple layers:

1. **SDK Wrapper** (`src/lib/odude-sdk-integration.ts`): Direct SDK interface
2. **Library Functions** (`src/lib/library.ts`): Backward-compatible functions
3. **Network Validation** (`src/lib/network-validation.ts`): Network switching utilities
4. **Wallet Validation** (`src/lib/wallet-validation.ts`): Wallet connection utilities

### Configuration

All SDK configuration is centralized in `src/lib/config.ts`:

```typescript
export const ODUDE_SDK_CONFIG = {
  NETWORK: 'basesepolia',
  RPC_URL: 'https://sepolia.base.org',
  CHAIN_ID: 84532,
  CONTRACTS: {
    Registry: '******************************************',
    Resolver: '******************************************',
    TLD: '******************************************',
    RWAirdrop: '******************************************'
  },
  BASE_TLD_PRICE: '1.0',
  DEFAULT_COMMISSION: 10,
};
```

## SDK Functions

### Initialization

```typescript
import { initializeODudeSDK, getODudeSDK } from '../lib/library';

// Initialize SDK
const sdk = initializeODudeSDK();

// Get existing SDK instance
const sdk = getODudeSDK();

// Connect wallet for write operations
await connectWalletToODudeSDK(window.ethereum);
```

### Domain Operations

#### Check Domain Existence
```typescript
const exists = await checkDomainExists('bitcoin@crypto');
console.log('Domain exists:', exists);
```

#### Check Domain Availability
```typescript
const available = await isDomainAvailable('bitcoin@crypto');
console.log('Domain available:', available);
```

#### Get Domain Information
```typescript
const info = await getNameInfo('bitcoin@crypto');
console.log('Domain info:', info);
```

#### Get Detailed Domain Information
```typescript
const details = await getNameDetails('bitcoin@crypto');
console.log('Domain details:', details);
```

### Resolution Operations

#### Resolve Name to Address
```typescript
const address = await resolveName('bitcoin@crypto');
console.log('Resolved address:', address);
```

#### Reverse Resolve Address to Name
```typescript
const name = await reverseResolve('******************************************');
console.log('Primary name:', name);
```

### Address Operations

#### Get Names List for Address
```typescript
const names = await getNamesList('******************************************');
console.log('Owned names:', names);
```

### Contract Information

#### Get Contract Information
```typescript
const contractInfo = await getContractInfo();
console.log('Contract info:', contractInfo);
```

#### Get Total Supply
```typescript
const totalSupply = await getTotalSupply();
console.log('Total supply:', totalSupply);
```

### Airdrop Operations

#### Get Airdrop Information
```typescript
const airdropInfo = await getAirdropInfo('******************************************');
console.log('Airdrop info:', airdropInfo);
```

## Network Validation

### Automatic Network Detection

The application automatically detects the connected network and validates it:

```typescript
import { useWalletValidation } from '../lib/wallet-validation';

const walletValidation = useWalletValidation();

if (!walletValidation.isCorrectNetwork) {
  // Prompt user to switch network
  await walletValidation.switchToCorrectNetwork();
}
```

### Network Switching

```typescript
import { validateAndSwitchNetwork } from '../lib/network-validation';

// Validate and switch if needed
const success = await validateAndSwitchNetwork(currentChainId);
```

## Error Handling

### SDK Errors

All SDK functions include comprehensive error handling:

```typescript
try {
  const result = await checkDomainExists('test.domain');
  console.log('Success:', result);
} catch (error) {
  console.error('SDK Error:', error);
  // Handle error appropriately
}
```

### Network Errors

Network validation includes automatic error handling with user notifications:

```typescript
// Automatic notification for wrong network
if (!isBaseSepolia(chainId)) {
  showNetworkWarning(chainId);
}
```

## Testing

### SDK Test Page

The application includes a comprehensive test page at `/test-sdk` that provides:

1. **SDK Initialization Testing**
2. **Contract Information Display**
3. **Domain Function Testing**
4. **Address Function Testing**
5. **Airdrop Function Testing**
6. **Real-time Result Display**

### Test Categories

#### Domain Tests
- Domain existence checking
- Domain availability checking
- Name information retrieval
- Name details retrieval
- Name resolution

#### Address Tests
- Reverse resolution
- Names list retrieval

#### Contract Tests
- Total supply retrieval
- Contract information refresh

#### Airdrop Tests
- Airdrop information retrieval

### Using the Test Interface

1. Navigate to `/test-sdk`
2. Connect wallet to Base Sepolia
3. Initialize SDK
4. Use tabbed interface to test functions
5. View real-time results
6. Copy results for debugging

## Migration from Previous Version

### Changes Made

1. **Package Replacement**: `@odude/oduderesolve` → `odude-sdk`
2. **Network Change**: Multiple networks → Base Sepolia only
3. **Contract Updates**: New contract addresses on Base Sepolia
4. **Function Updates**: Mock functions → Real SDK calls

### Breaking Changes

- Network configuration now defaults to Base Sepolia
- Some mock functions replaced with real implementations
- Contract addresses updated for Base Sepolia deployment

### Backward Compatibility

The integration maintains backward compatibility by:
- Keeping existing function signatures
- Providing fallback values for missing functionality
- Maintaining the same return types

## Best Practices

### SDK Usage

1. **Always Initialize**: Ensure SDK is initialized before use
2. **Network Validation**: Validate network before operations
3. **Error Handling**: Implement comprehensive error handling
4. **Wallet Connection**: Connect wallet for write operations

### Performance

1. **Singleton Pattern**: SDK uses singleton pattern for efficiency
2. **Caching**: Results are cached where appropriate
3. **Batch Operations**: Use batch operations when possible

### Security

1. **Network Validation**: Always validate network before operations
2. **Address Validation**: Validate addresses before use
3. **Input Sanitization**: Sanitize user inputs

## Troubleshooting

### Common Issues

1. **SDK Not Initialized**
   - Solution: Call `initializeODudeSDK()` first

2. **Wrong Network**
   - Solution: Switch to Base Sepolia (Chain ID: 84532)

3. **Contract Call Failures**
   - Solution: Ensure sufficient gas and correct network

4. **Wallet Not Connected**
   - Solution: Connect wallet before write operations

### Debug Information

Enable debug logging by setting `NODE_ENV=development`:

```typescript
// Logs are automatically enabled in development
consoleLog('SDK operation', { data });
```

## Future Enhancements

### Planned Features

1. **Write Operations**: Implement minting and management functions
2. **Batch Operations**: Add batch processing capabilities
3. **Caching Layer**: Implement advanced caching
4. **Event Listening**: Add contract event monitoring

### SDK Improvements Needed

Based on testing, the following improvements are suggested for the odude-sdk:

1. **TLD Management**: Add functions for ERC token binding and price setting
2. **Minting Functions**: Implement domain minting functionality
3. **Event Filters**: Add event filtering capabilities
4. **Batch Queries**: Add batch query functions for efficiency

## Support

For issues related to the SDK integration:

1. Use the test page to verify functionality
2. Check browser console for detailed logs
3. Verify network and wallet configuration
4. Review this documentation for proper usage patterns
