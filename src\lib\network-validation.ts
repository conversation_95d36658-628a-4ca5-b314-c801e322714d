// src/lib/network-validation.ts
// Network validation and switching utilities for Base Sepolia

import { baseSepolia } from 'wagmi/chains';
import { switchChain } from '@wagmi/core';
import { config } from './config';
import { ODUDE_SDK_CONFIG, consoleLog, consoleError } from './config';
import { notifications } from '@mantine/notifications';

/**
 * Check if the current network is Base Sepolia
 */
export function isBaseSepolia(chainId: number): boolean {
  return chainId === ODUDE_SDK_CONFIG.CHAIN_ID;
}

/**
 * Get network name by chain ID
 */
export function getNetworkName(chainId: number): string {
  switch (chainId) {
    case 84532:
      return 'Base Sepolia';
    case 8453:
      return 'Base';
    case 1:
      return 'Ethereum Mainnet';
    case 137:
      return 'Polygon';
    case 42161:
      return 'Arbitrum';
    case 10:
      return 'Optimism';
    case 11155111:
      return 'Sepolia';
    default:
      return `Unknown Network (${chainId})`;
  }
}

/**
 * Switch to Base Sepolia network
 */
export async function switchToBaseSepolia(): Promise<boolean> {
  consoleLog('Attempting to switch to Base Sepolia');
  
  try {
    await switch<PERSON>hain(config, { chainId: baseSepolia.id });
    
    notifications.show({
      title: 'Network Switched',
      message: 'Successfully switched to Base Sepolia network',
      color: 'green',
    });
    
    consoleLog('Successfully switched to Base Sepolia');
    return true;
  } catch (error) {
    consoleError('Failed to switch to Base Sepolia', error);
    
    notifications.show({
      title: 'Network Switch Failed',
      message: 'Failed to switch to Base Sepolia. Please switch manually in your wallet.',
      color: 'red',
    });
    
    return false;
  }
}

/**
 * Validate network and prompt user to switch if needed
 */
export async function validateAndSwitchNetwork(currentChainId: number): Promise<boolean> {
  consoleLog('Validating network', { currentChainId, required: ODUDE_SDK_CONFIG.CHAIN_ID });
  
  if (isBaseSepolia(currentChainId)) {
    consoleLog('Already connected to Base Sepolia');
    return true;
  }
  
  const currentNetworkName = getNetworkName(currentChainId);
  consoleLog('Wrong network detected', { current: currentNetworkName, required: 'Base Sepolia' });
  
  // Show notification about wrong network
  notifications.show({
    title: 'Wrong Network',
    message: `You're connected to ${currentNetworkName}. ODude requires Base Sepolia network.`,
    color: 'orange',
    autoClose: false,
  });
  
  // Attempt to switch automatically
  return await switchToBaseSepolia();
}

/**
 * Check if wallet supports network switching
 */
export function supportsNetworkSwitching(): boolean {
  return typeof window !== 'undefined' && 
         window.ethereum && 
         typeof window.ethereum.request === 'function';
}

/**
 * Add Base Sepolia network to wallet if not present
 */
export async function addBaseSepolia(): Promise<boolean> {
  if (!supportsNetworkSwitching()) {
    return false;
  }
  
  try {
    await window.ethereum.request({
      method: 'wallet_addEthereumChain',
      params: [{
        chainId: `0x${ODUDE_SDK_CONFIG.CHAIN_ID.toString(16)}`, // 0x14a34
        chainName: 'Base Sepolia',
        nativeCurrency: {
          name: 'Ethereum',
          symbol: 'ETH',
          decimals: 18,
        },
        rpcUrls: [ODUDE_SDK_CONFIG.RPC_URL],
        blockExplorerUrls: ['https://sepolia.basescan.org/'],
      }],
    });
    
    notifications.show({
      title: 'Network Added',
      message: 'Base Sepolia network has been added to your wallet',
      color: 'green',
    });
    
    return true;
  } catch (error) {
    consoleError('Failed to add Base Sepolia network', error);
    
    notifications.show({
      title: 'Failed to Add Network',
      message: 'Could not add Base Sepolia network to your wallet',
      color: 'red',
    });
    
    return false;
  }
}

/**
 * Network validation hook for components
 */
export interface NetworkValidationResult {
  isCorrectNetwork: boolean;
  currentChainId: number;
  currentNetworkName: string;
  requiredNetworkName: string;
  switchNetwork: () => Promise<boolean>;
  addNetwork: () => Promise<boolean>;
}

export function useNetworkValidation(chainId: number): NetworkValidationResult {
  const isCorrectNetwork = isBaseSepolia(chainId);
  const currentNetworkName = getNetworkName(chainId);
  const requiredNetworkName = 'Base Sepolia';
  
  return {
    isCorrectNetwork,
    currentChainId: chainId,
    currentNetworkName,
    requiredNetworkName,
    switchNetwork: switchToBaseSepolia,
    addNetwork: addBaseSepolia,
  };
}

/**
 * Show network warning notification
 */
export function showNetworkWarning(currentChainId: number): void {
  const currentNetworkName = getNetworkName(currentChainId);
  
  notifications.show({
    id: 'network-warning',
    title: 'Wrong Network Detected',
    message: `You're connected to ${currentNetworkName}. Please switch to Base Sepolia to use ODude features.`,
    color: 'orange',
    autoClose: false,
    withCloseButton: true,
  });
}

/**
 * Hide network warning notification
 */
export function hideNetworkWarning(): void {
  notifications.hide('network-warning');
}

/**
 * Network status component data
 */
export interface NetworkStatus {
  chainId: number;
  networkName: string;
  isSupported: boolean;
  isRequired: boolean;
  canSwitch: boolean;
}

export function getNetworkStatus(chainId: number): NetworkStatus {
  return {
    chainId,
    networkName: getNetworkName(chainId),
    isSupported: chainId === ODUDE_SDK_CONFIG.CHAIN_ID,
    isRequired: true, // Base Sepolia is required for ODude
    canSwitch: supportsNetworkSwitching(),
  };
}

/**
 * Validate network on app initialization
 */
export async function initializeNetworkValidation(chainId: number): Promise<void> {
  consoleLog('Initializing network validation', { chainId });
  
  if (!isBaseSepolia(chainId)) {
    showNetworkWarning(chainId);
  } else {
    hideNetworkWarning();
    consoleLog('Network validation passed - connected to Base Sepolia');
  }
}

// Export constants for external use
export const REQUIRED_CHAIN_ID = ODUDE_SDK_CONFIG.CHAIN_ID;
export const REQUIRED_NETWORK_NAME = 'Base Sepolia';
export const REQUIRED_RPC_URL = ODUDE_SDK_CONFIG.RPC_URL;
