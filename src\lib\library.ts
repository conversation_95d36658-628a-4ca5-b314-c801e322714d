// src/lib/library.ts
// Common functions for ODude Name management based on project.txt

import { ODUDENAME_CONFIG, ODUDE_SDK_CONFIG, consoleLog, consoleError } from './config';
import * as SDKIntegration from './odude-sdk-integration';

// Network configuration type
export interface NetworkConfig {
  id: string;
  name: string;
  sign: string;
  contract: string;
  node: string;
  explorer: string;
  price: number;
  banner: string;
}

// Domain validation result
export interface DomainValidationResult {
  isValid: boolean;
  error?: string;
}

// API response types
export interface ChainResponse {
  chain: string;
}

export interface DomainResponse {
  error?: string;
  [key: string]: any;
}

/**
 * Split domain by subdomain or primary part
 * Based on PHP split_domain function
 */
export function splitDomain(title: string, part: 'subdomain' | 'primary'): string {
  consoleLog('splitDomain called', { title, part });
  
  if (part === 'subdomain') {
    const subdomain = title.split('.', 2);
    if (subdomain[0]) {
      return subdomain[0]; // e.g., "navneet" from "navneet.crypto"
    }
  } else if (part === 'primary') {
    const subdomain = title.split('.', 2);
    if (subdomain[1]) {
      return subdomain[1]; // e.g., "crypto" from "navneet.crypto"
    }
  }
  
  return title;
}

/**
 * Validate domain name
 * Based on PHP is_valid_domain_name function
 */
export function isValidDomainName(domainName: string): DomainValidationResult {
  consoleLog('isValidDomainName called', { domainName });
  
  const dotCount = (domainName.match(/\./g) || []).length;
  if (dotCount > 1) {
    return { isValid: false, error: 'Too many dots in domain name' };
  }

  // Valid chars check
  const validCharsRegex = /^([a-z\d](-*[a-z\d])*)(\.([a-z\d](-*[a-z\d])*))*$/i;
  if (!validCharsRegex.test(domainName)) {
    return { isValid: false, error: 'Invalid characters in domain name' };
  }

  // Overall length check
  const overallLengthRegex = /^.{1,253}$/;
  if (!overallLengthRegex.test(domainName)) {
    return { isValid: false, error: 'Domain name too long' };
  }

  // Length of each label
  const labelLengthRegex = /^[^\.]{1,63}(\.[^\.]{1,63})*$/;
  if (!labelLengthRegex.test(domainName)) {
    return { isValid: false, error: 'Domain label too long' };
  }

  return { isValid: true };
}

/**
 * Check if string contains special characters
 * Helper function for domain validation
 */
export function containsSpecialCharacters(str: string): boolean {
  const specialCharsRegex = /[^a-zA-Z0-9.-]/;
  return specialCharsRegex.test(str);
}

/**
 * Get chain value from TLN endpoint
 * Based on PHP getChainValue function
 */
export async function getChainValue(tln: string): Promise<string | null> {
  consoleLog('getChainValue called', { tln });
  
  try {
    const url = `${ODUDENAME_CONFIG.TLN_ENDPOINT}?tln=${encodeURIComponent(tln)}`;
    const response = await fetch(url);
    
    if (!response.ok) {
      consoleError('Failed to fetch chain value', response.statusText);
      return null;
    }
    
    const data: ChainResponse[] = await response.json();
    
    if (Array.isArray(data) && data.length > 0 && data[0].chain) {
      consoleLog('Chain value retrieved', data[0].chain);
      return data[0].chain;
    }
    
    return null;
  } catch (error) {
    consoleError('Error fetching chain value', error);
    return null;
  }
}

/**
 * Get contract configuration based on domain/chain
 * Updated to use Base Sepolia as default for ODude SDK
 */
export async function getContractConfig(title: string): Promise<NetworkConfig> {
  consoleLog('getContractConfig called', { title });

  const primary = splitDomain(title.toLowerCase().trim(), 'primary') || title;
  const chain = await getChainValue(primary);

  consoleLog('Chain determined', { primary, chain });

  // For ODude SDK, we primarily use Base Sepolia
  if (chain === '314') {
    return ODUDENAME_CONFIG.NETWORKS.FILECOIN;
  } else if (chain === '80001') {
    return ODUDENAME_CONFIG.NETWORKS.MUMBAI;
  } else if (chain === '137') {
    return ODUDENAME_CONFIG.NETWORKS.POLYGON;
  } else {
    // Default to Base Sepolia for ODude SDK
    return ODUDENAME_CONFIG.NETWORKS.BASE_SEPOLIA;
  }
}

/**
 * Check API for domain information
 * Based on PHP API checking functionality
 */
export async function checkApi(url: string): Promise<DomainResponse | null> {
  consoleLog('checkApi called', { url });
  
  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      consoleError('API request failed', response.statusText);
      return null;
    }
    
    const data: DomainResponse = await response.json();
    consoleLog('API response received', data);
    
    return data;
  } catch (error) {
    consoleError('Error checking API', error);
    return null;
  }
}

/**
 * Generate unique ID for claims
 * Helper function for minting process
 */
export function generateUniqueId(): string {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

/**
 * Format address for display
 * Helper function to truncate wallet addresses
 */
export function formatAddress(address: string, startLength = 6, endLength = 4): string {
  if (!address) return '';
  if (address.length <= startLength + endLength) return address;
  
  return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
}

/**
 * Sleep function for async operations
 * Helper function for contract interactions
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Validate Ethereum address
 * Helper function for address validation
 */
export function isValidEthereumAddress(address: string): boolean {
  const ethereumAddressRegex = /^0x[a-fA-F0-9]{40}$/;
  return ethereumAddressRegex.test(address);
}

/**
 * Get network name by chain ID
 * Helper function for network identification
 */
export function getNetworkNameByChainId(chainId: string): string {
  const networks = ODUDENAME_CONFIG.NETWORKS;
  
  for (const [key, network] of Object.entries(networks)) {
    if (network.id === chainId) {
      return network.name;
    }
  }
  
  return 'Unknown Network';
}

/**
 * Build domain URL with parameters
 * Helper function for URL construction
 */
export function buildDomainUrl(baseUrl: string, domain: string, chain: string): string {
  const url = new URL(baseUrl);
  url.searchParams.set('domain', domain);
  url.searchParams.set('chain', chain);
  return url.toString();
}

// Contract interaction types and interfaces
export interface ContractCallResult {
  success: boolean;
  data?: any;
  error?: string;
}

/**
 * Real contract interaction functions using ODude SDK
 * These replace the previous mock implementations
 */

/**
 * Get domain ID by name using ODude SDK
 */
export async function getDomainId(domainName: string): Promise<number | null> {
  return await SDKIntegration.getDomainId(domainName);
}

/**
 * Get domain owner by token ID using ODude SDK
 */
export async function getDomainOwner(tokenId: number): Promise<string | null> {
  return await SDKIntegration.getDomainOwner(tokenId);
}

/**
 * Get domain owner by name using ODude SDK
 */
export async function getDomainOwnerByName(domainName: string): Promise<string | null> {
  return await SDKIntegration.getDomainOwnerByName(domainName);
}

/**
 * Get domain mint price by token ID
 * Using TLD base price from ODude SDK
 */
export async function getDomainMintPrice(tokenId: number): Promise<string | null> {
  consoleLog('getDomainMintPrice called', { tokenId });

  try {
    // Get base TLD price from contract
    const price = await SDKIntegration.getTLDPrice();
    return price;
  } catch (error) {
    consoleError('Error getting domain mint price', error);
    return ODUDE_SDK_CONFIG.BASE_TLD_PRICE;
  }
}

/**
 * Get ERC token address for domain
 * Note: This functionality may need to be implemented in TLD contract
 */
export async function getDomainErcAddress(tokenId: number): Promise<string | null> {
  consoleLog('getDomainErcAddress called', { tokenId });

  // TODO: Implement this in TLD contract if needed
  // For now, return zero address as default
  return '******************************************';
}

/**
 * Get commission percentage for domain
 * Using default commission from ODude SDK
 */
export async function getDomainCommission(tokenId: number): Promise<number | null> {
  consoleLog('getDomainCommission called', { tokenId });

  try {
    const commission = await SDKIntegration.getDefaultCommission();
    return commission;
  } catch (error) {
    consoleError('Error getting domain commission', error);
    return ODUDE_SDK_CONFIG.DEFAULT_COMMISSION;
  }
}

/**
 * Set ERC token address for domain
 * TODO: Implement this in TLD contract if needed
 */
export async function setDomainErcAddress(tokenId: number, ercAddress: string): Promise<ContractCallResult> {
  consoleLog('setDomainErcAddress called', { tokenId, ercAddress });

  // TODO: Implement this functionality in TLD contract
  // For now, simulate success
  await sleep(2000);
  return { success: true, error: 'Function not yet implemented in TLD contract' };
}

/**
 * Set domain mint price
 * TODO: Implement this in TLD contract if needed
 */
export async function setDomainMintPrice(tokenId: number, price: string): Promise<ContractCallResult> {
  consoleLog('setDomainMintPrice called', { tokenId, price });

  // TODO: Implement this functionality in TLD contract
  // For now, simulate success
  await sleep(2000);
  return { success: true, error: 'Function not yet implemented in TLD contract' };
}

/**
 * Claim/mint a domain
 * TODO: Implement this using TLD contract minting functionality
 */
export async function claimDomain(
  tokenId: number,
  domainName: string,
  tokenUri: string,
  toAddress: string
): Promise<ContractCallResult> {
  consoleLog('claimDomain called', { tokenId, domainName, tokenUri, toAddress });

  // TODO: Implement actual minting using TLD contract
  // This would require implementing the minting functionality in the SDK

  // For now, simulate the process
  await sleep(3000);
  return { success: true, error: 'Minting function not yet implemented in TLD contract' };
}

/**
 * Check if wallet is connected to the correct network (Base Sepolia)
 * Helper function for network validation
 */
export function isCorrectNetwork(currentChainId: number, requiredChainId: string = ODUDE_SDK_CONFIG.CHAIN_ID.toString()): boolean {
  return currentChainId.toString() === requiredChainId;
}

/**
 * Check if wallet is connected to Base Sepolia
 */
export function isConnectedToBaseSepolia(currentChainId: number): boolean {
  return currentChainId === ODUDE_SDK_CONFIG.CHAIN_ID;
}

/**
 * Get network configuration by chain ID
 * Helper function to get network config
 */
export function getNetworkConfigByChainId(chainId: string): NetworkConfig | null {
  const networks = ODUDENAME_CONFIG.NETWORKS;

  for (const network of Object.values(networks)) {
    if (network.id === chainId) {
      return network;
    }
  }

  return null;
}

/**
 * Format price for display
 * Helper function to format token amounts
 */
export function formatPrice(price: string, decimals: number = 18): string {
  try {
    const priceNum = parseFloat(price);
    const divisor = Math.pow(10, decimals);
    const formatted = (priceNum / divisor).toFixed(4);
    return parseFloat(formatted).toString(); // Remove trailing zeros
  } catch {
    return price;
  }
}

/**
 * Validate token address format
 * Helper function for ERC20 address validation
 */
export function isValidTokenAddress(address: string): boolean {
  if (!address) return false;

  // Allow zero address
  if (address === '******************************************') {
    return true;
  }

  return isValidEthereumAddress(address);
}

/**
 * Generate token URI for domain
 * Helper function to create metadata URI
 */
export function generateTokenUri(domain: string): string {
  // In real implementation, this would point to actual metadata
  return `https://odude.com/temp/${domain.toLowerCase()}.json`;
}

/**
 * Parse error message from contract call
 * Helper function to extract readable error messages
 */
export function parseContractError(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.message) {
    return error.message;
  }

  if (error?.reason) {
    return error.reason;
  }

  return 'Unknown error occurred';
}

/**
 * Initialize ODude SDK
 */
export function initializeODudeSDK(provider?: any) {
  return SDKIntegration.initializeSDK(provider);
}

/**
 * Get ODude SDK instance
 */
export function getODudeSDK(provider?: any) {
  return SDKIntegration.getSDK(provider);
}

/**
 * Connect wallet to ODude SDK
 */
export async function connectWalletToODudeSDK(provider: any) {
  return await SDKIntegration.connectWalletToSDK(provider);
}

/**
 * Check if domain exists using ODude SDK
 */
export async function checkDomainExists(domainName: string): Promise<boolean> {
  return await SDKIntegration.checkDomainExists(domainName);
}

/**
 * Check if domain is available using ODude SDK
 */
export async function isDomainAvailable(domainName: string): Promise<boolean> {
  return await SDKIntegration.isDomainAvailable(domainName);
}

/**
 * Get comprehensive name information using ODude SDK
 */
export async function getNameInfo(domainName: string): Promise<any> {
  return await SDKIntegration.getNameInfo(domainName);
}

/**
 * Get detailed name information using ODude SDK
 */
export async function getNameDetails(domainName: string): Promise<any> {
  return await SDKIntegration.getNameDetails(domainName);
}

/**
 * Resolve name to address using ODude SDK
 */
export async function resolveName(domainName: string): Promise<string | null> {
  return await SDKIntegration.resolveName(domainName);
}

/**
 * Reverse resolve address to name using ODude SDK
 */
export async function reverseResolve(address: string): Promise<string | null> {
  return await SDKIntegration.reverseResolve(address);
}

/**
 * Get airdrop information using ODude SDK
 */
export async function getAirdropInfo(address: string): Promise<any> {
  return await SDKIntegration.getAirdropInfo(address);
}

/**
 * Get names list for address using ODude SDK
 */
export async function getNamesList(address: string): Promise<Array<{tokenId: string, name: string}>> {
  return await SDKIntegration.getNamesList(address);
}

/**
 * Get contract information using ODude SDK
 */
export async function getContractInfo(): Promise<any> {
  return await SDKIntegration.getContractInfo();
}
