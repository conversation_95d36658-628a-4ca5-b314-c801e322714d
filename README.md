# ODude Dealer

A comprehensive Top Level Name (TLN) management DApp built with Next.js and integrated with the ODude SDK for Base Sepolia network.

## Overview

ODude Dealer is a decentralized application that allows users to search, manage, and interact with ODude Name domains on the Base Sepolia network. The application provides a user-friendly interface for domain management and includes comprehensive testing tools for the ODude SDK.

## Features

- **Domain Search & Management**: Search for domain availability and view detailed domain information
- **Base Sepolia Integration**: Fully integrated with Base Sepolia network (Chain ID: 84532)
- **ODude SDK Integration**: Complete integration with the odude-sdk for contract interactions
- **Wallet Connection**: RainbowKit integration for seamless wallet connections
- **Network Validation**: Automatic network detection and switching to Base Sepolia
- **SDK Test Interface**: Comprehensive testing page for all ODude SDK functionality
- **Real-time Contract Data**: Live data from Registry, Resolver, TLD, and RWAirdrop contracts

## Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **UI Library**: Mantine v8 with dark theme
- **Wallet Integration**: RainbowKit v2, Wagmi v2
- **Blockchain**: Base Sepolia (Chain ID: 84532)
- **Smart Contracts**: ODude SDK integration
- **Styling**: Mantine components with custom theming

## Getting Started

### Prerequisites

- Node.js 16+
- npm, yarn, pnpm, or bun
- A Web3 wallet (MetaMask, WalletConnect, etc.)
- Base Sepolia testnet ETH for transactions

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd odude-dealer
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Start the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Wallet Setup

1. Connect your wallet using the "Connect Wallet" button
2. Ensure you're connected to Base Sepolia network (Chain ID: 84532)
3. If on wrong network, the app will prompt you to switch automatically
4. Get Base Sepolia testnet ETH from a faucet if needed

## ODude SDK Integration

### Contract Addresses (Base Sepolia)

- **Registry**: `******************************************`
- **Resolver**: `******************************************`
- **TLD**: `******************************************`
- **RWAirdrop**: `******************************************`

### SDK Features

The application integrates with the odude-sdk to provide:

- **Domain Resolution**: Resolve names to addresses and vice versa
- **Domain Management**: Check availability, get owner information
- **Registry Operations**: Access NFT metadata, token information
- **TLD Operations**: Get pricing, commission information
- **Airdrop Information**: Check airdrop eligibility and claims

## Application Pages

### 1. Dashboard (`/`)
- Overview of the application
- Quick access to main features
- Wallet connection status

### 2. Domain Info (`/info`)
- Search for domain availability
- View detailed domain information
- Domain owner management tools
- Network validation and switching

### 3. SDK Test Page (`/test-sdk`)
- Comprehensive testing interface for ODude SDK
- Test all contract functions with GUI
- Real-time results display
- Copy test results to clipboard

#### Test Categories:

**Domain Tests**:
- Check domain existence
- Check domain availability
- Get name information
- Get name details
- Resolve name to address

**Address Tests**:
- Reverse resolve address to name
- Get names list for address

**Contract Tests**:
- Get total supply
- Refresh contract information

**Airdrop Tests**:
- Get airdrop information for address

## Network Configuration

The application is configured for Base Sepolia network:

- **Chain ID**: 84532
- **RPC URL**: https://sepolia.base.org
- **Explorer**: https://sepolia.basescan.org/
- **Native Token**: ETH

### Network Switching

The app automatically detects the connected network and prompts users to switch to Base Sepolia if needed. Network switching is handled through:

- Automatic detection of wrong network
- User-friendly prompts to switch
- Automatic network addition if not present in wallet

## Development

### Project Structure

```
src/
├── app/                    # Next.js app router pages
│   ├── info/              # Domain info page
│   ├── test-sdk/          # SDK test page
│   └── providers.tsx      # App providers
├── components/            # React components
│   ├── AppShellLayout.tsx # Main layout component
│   ├── WalletStatus.tsx   # Wallet status component
│   └── LoginPage.tsx      # Login page component
├── lib/                   # Utility libraries
│   ├── config.ts          # App configuration
│   ├── library.ts         # Common functions
│   ├── odude-sdk-integration.ts # SDK integration layer
│   ├── network-validation.ts    # Network validation utilities
│   └── wallet-validation.ts     # Wallet validation utilities
└── types/                 # TypeScript type definitions
```

### Key Configuration Files

- `src/lib/config.ts`: Main configuration including network settings
- `src/lib/odude-sdk-integration.ts`: SDK wrapper functions
- `src/app/providers.tsx`: Wagmi and RainbowKit configuration

### Environment Variables

Create a `.env.local` file with:

```env
NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID=your_project_id
```

## Testing

### Using the SDK Test Page

1. Navigate to `/test-sdk`
2. Ensure wallet is connected to Base Sepolia
3. Initialize the SDK using the "Initialize SDK" button
4. Use the tabbed interface to test different functionalities:
   - **Domain Tests**: Test domain-related functions
   - **Address Tests**: Test address-related functions
   - **Contract Tests**: Test contract information functions
   - **Airdrop Tests**: Test airdrop-related functions

### Test Results

- All test results are displayed in real-time
- Results include success/failure status, data, and timestamps
- Copy individual results to clipboard for debugging
- Clear all results with the "Clear Results" button

## Troubleshooting

### Common Issues

1. **Wrong Network**: Ensure you're connected to Base Sepolia (Chain ID: 84532)
2. **SDK Not Initialized**: Check that the SDK initializes properly on the test page
3. **Wallet Connection**: Ensure your wallet is properly connected
4. **Contract Calls Failing**: Verify you have sufficient Base Sepolia ETH for gas

### Debug Information

The application includes comprehensive logging in development mode. Check the browser console for detailed information about:

- Wallet connection status
- Network validation
- SDK initialization
- Contract call results

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly using the SDK test page
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Check the SDK test page for functionality verification
- Review browser console logs for debugging information
- Ensure proper network and wallet configuration
